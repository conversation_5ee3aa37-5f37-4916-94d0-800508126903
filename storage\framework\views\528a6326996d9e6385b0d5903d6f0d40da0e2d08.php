

<?php $__env->startSection('title', 'จัดการบริการ - Admin Panel'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-tools me-2"></i>จัดการบริการ
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">แดชบอร์ด</a></li>
                        <li class="breadcrumb-item active">จัดการบริการ</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">รายการบริการทั้งหมด</h3>
                            <div class="card-tools">
                                <div class="input-group input-group-sm" style="width: 200px;">
                                    <input type="text" name="search" class="form-control float-right" placeholder="ค้นหาบริการ..." id="searchInput">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-default">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <a href="<?php echo e(route('admin.services.create')); ?>" class="btn btn-primary ml-2">
                                    <i class="fas fa-plus me-1"></i>เพิ่มบริการใหม่
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if(session('success')): ?>
                                <div class="alert alert-success alert-dismissible">
                                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                                    <?php echo e(session('success')); ?>

                                </div>
                            <?php endif; ?>

                            <?php if($services->count() > 0): ?>
                                <!-- Bulk Actions -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="bulk-actions" style="display: none;">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-danger" onclick="bulkDelete()">
                                                    <i class="fas fa-trash me-1"></i>ลบที่เลือก
                                                </button>
                                                <button type="button" class="btn btn-sm btn-secondary" onclick="clearSelection()">
                                                    <i class="fas fa-times me-1"></i>ยกเลิกการเลือก
                                                </button>
                                            </div>
                                            <span class="ml-2 text-muted">เลือกแล้ว: <span id="selectedCount">0</span> รายการ</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th style="width: 40px;">
                                                    <div class="custom-control custom-checkbox">
                                                        <input type="checkbox" class="custom-control-input" id="selectAll">
                                                        <label class="custom-control-label" for="selectAll"></label>
                                                    </div>
                                                </th>
                                                <th style="width: 120px;">รูปภาพ</th>
                                                <th>หัวข้อ</th>
                                                <th>รายละเอียด</th>
                                                <th style="width: 120px;">ราคา</th>
                                                <th style="width: 180px;">จัดการ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td class="text-center">
                                                        <div class="custom-control custom-checkbox">
                                                            <input type="checkbox" class="custom-control-input service-checkbox"
                                                                   id="service<?php echo e($service->id); ?>" value="<?php echo e($service->id); ?>">
                                                            <label class="custom-control-label" for="service<?php echo e($service->id); ?>"></label>
                                                        </div>
                                                    </td>
                                                    <td class="text-center">
                                                        <img src="<?php echo e(\App\Helpers\ImageHelper::getImageUrl($service->image)); ?>"
                                                             class="img-thumbnail"
                                                             style="width: 100px; height: 70px; object-fit: cover;"
                                                             alt="รูปภาพบริการ">
                                                    </td>
                                                    <td>
                                                        <strong><?php echo e($service->title); ?></strong>
                                                    </td>
                                                    <td>
                                                        <?php echo e(Str::limit($service->description, 80)); ?>

                                                    </td>
                                                    <td class="text-right">
                                                        <span class="badge badge-success">
                                                            ฿<?php echo e(number_format($service->price, 0)); ?>

                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <button type="button"
                                                                    class="btn btn-sm btn-info quick-view-btn"
                                                                    data-service-id="<?php echo e($service->id); ?>"
                                                                    data-toggle="tooltip"
                                                                    title="ดูรายละเอียด">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button type="button"
                                                                    class="btn btn-sm btn-warning quick-edit-btn"
                                                                    data-service-id="<?php echo e($service->id); ?>"
                                                                    data-service-title="<?php echo e($service->title); ?>"
                                                                    data-service-description="<?php echo e($service->description); ?>"
                                                                    data-service-price="<?php echo e($service->price); ?>"
                                                                    data-toggle="tooltip"
                                                                    title="แก้ไขด่วน">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <a href="<?php echo e(route('admin.services.edit', $service)); ?>"
                                                               class="btn btn-sm btn-secondary"
                                                               data-toggle="tooltip"
                                                               title="แก้ไขแบบเต็ม">
                                                                <i class="fas fa-cog"></i>
                                                            </a>
                                                            <form action="<?php echo e(route('admin.services.destroy', $service)); ?>"
                                                                  method="POST"
                                                                  style="display:inline;"
                                                                  onsubmit="return confirm('ยืนยันการลบบริการ <?php echo e($service->title); ?>?')">
                                                                <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                                                <button type="submit"
                                                                        class="btn btn-sm btn-danger"
                                                                        data-toggle="tooltip"
                                                                        title="ลบ">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">ยังไม่มีบริการ</h5>
                                    <p class="text-muted">เริ่มต้นโดยการเพิ่มบริการแรกของคุณ</p>
                                    <a href="<?php echo e(route('admin.services.create')); ?>" class="btn btn-primary">
                                        <i class="fas fa-plus me-1"></i>เพิ่มบริการใหม่
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
// Search functionality
document.getElementById('searchInput').addEventListener('keyup', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('tbody tr');

    tableRows.forEach(row => {
        const title = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        const description = row.querySelector('td:nth-child(3)').textContent.toLowerCase();

        if (title.includes(searchTerm) || description.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// Quick View Modal
document.addEventListener('DOMContentLoaded', function() {
    // Quick View functionality
    document.querySelectorAll('.quick-view-btn').forEach(button => {
        button.addEventListener('click', function() {
            const serviceId = this.dataset.serviceId;
            // Fetch service details and show in modal
            fetch(`/admin/services/${serviceId}`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('viewServiceTitle').textContent = data.title;
                    document.getElementById('viewServiceDescription').textContent = data.description;
                    document.getElementById('viewServicePrice').textContent = '฿' + new Intl.NumberFormat().format(data.price);
                    document.getElementById('viewServiceImage').src = data.image_url;
                    $('#quickViewModal').modal('show');
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการโหลดข้อมูล');
                });
        });
    });

    // Quick Edit functionality
    document.querySelectorAll('.quick-edit-btn').forEach(button => {
        button.addEventListener('click', function() {
            const serviceId = this.dataset.serviceId;
            const title = this.dataset.serviceTitle;
            const description = this.dataset.serviceDescription;
            const price = this.dataset.servicePrice;

            document.getElementById('editServiceId').value = serviceId;
            document.getElementById('editServiceTitle').value = title;
            document.getElementById('editServiceDescription').value = description;
            document.getElementById('editServicePrice').value = price;

            $('#quickEditModal').modal('show');
        });
    });

    // Quick Edit Form submission
    document.getElementById('quickEditForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const serviceId = document.getElementById('editServiceId').value;
        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');

        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังบันทึก...';
        submitBtn.disabled = true;

        fetch(`/admin/services/${serviceId}`, {
            method: 'PUT',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                title: formData.get('title'),
                description: formData.get('description'),
                price: formData.get('price')
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                $('#quickEditModal').modal('hide');
                location.reload(); // Reload to show updated data
            } else {
                alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถบันทึกได้'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('เกิดข้อผิดพลาดในการบันทึกข้อมูล');
        })
        .finally(() => {
            // Reset button state
            submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>บันทึก';
            submitBtn.disabled = false;
        });
    });

    // Bulk Actions functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const serviceCheckboxes = document.querySelectorAll('.service-checkbox');
    const bulkActionsDiv = document.querySelector('.bulk-actions');
    const selectedCountSpan = document.getElementById('selectedCount');

    // Select All functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            serviceCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }

    // Individual checkbox functionality
    serviceCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActions();

            // Update select all checkbox
            const checkedCount = document.querySelectorAll('.service-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === serviceCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < serviceCheckboxes.length;
        });
    });

    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.service-checkbox:checked');
        const count = checkedBoxes.length;

        if (count > 0) {
            bulkActionsDiv.style.display = 'block';
            selectedCountSpan.textContent = count;
        } else {
            bulkActionsDiv.style.display = 'none';
        }
    }

    // Bulk delete function
    window.bulkDelete = function() {
        const checkedBoxes = document.querySelectorAll('.service-checkbox:checked');
        const ids = Array.from(checkedBoxes).map(cb => cb.value);

        if (ids.length === 0) {
            alert('กรุณาเลือกรายการที่ต้องการลบ');
            return;
        }

        if (confirm(`ยืนยันการลบบริการ ${ids.length} รายการ?`)) {
            // Send bulk delete request
            fetch('/admin/services/bulk-delete', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ ids: ids })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('เกิดข้อผิดพลาด: ' + (data.message || 'ไม่สามารถลบได้'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('เกิดข้อผิดพลาดในการลบข้อมูล');
            });
        }
    };

    // Clear selection function
    window.clearSelection = function() {
        serviceCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
        updateBulkActions();
    };

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
});
</script>

<!-- Quick View Modal -->
<div class="modal fade" id="quickViewModal" tabindex="-1" role="dialog" aria-labelledby="quickViewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickViewModalLabel">
                    <i class="fas fa-eye me-2"></i>รายละเอียดบริการ
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <img id="viewServiceImage" src="" alt="รูปภาพบริการ" class="img-fluid rounded">
                    </div>
                    <div class="col-md-8">
                        <h4 id="viewServiceTitle" class="mb-3"></h4>
                        <p id="viewServiceDescription" class="text-muted"></p>
                        <h5 class="text-success">
                            <i class="fas fa-tag me-2"></i>ราคา: <span id="viewServicePrice"></span>
                        </h5>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">ปิด</button>
            </div>
        </div>
    </div>
</div>

<!-- Quick Edit Modal -->
<div class="modal fade" id="quickEditModal" tabindex="-1" role="dialog" aria-labelledby="quickEditModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickEditModalLabel">
                    <i class="fas fa-edit me-2"></i>แก้ไขบริการด่วน
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="quickEditForm">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <div class="modal-body">
                    <input type="hidden" id="editServiceId" name="service_id">

                    <div class="form-group">
                        <label for="editServiceTitle">ชื่อบริการ</label>
                        <input type="text" class="form-control" id="editServiceTitle" name="title" required>
                    </div>

                    <div class="form-group">
                        <label for="editServiceDescription">รายละเอียด</label>
                        <textarea class="form-control" id="editServiceDescription" name="description" rows="3" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="editServicePrice">ราคา (บาท)</label>
                        <input type="number" class="form-control" id="editServicePrice" name="price" min="0" step="0.01" required>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        หากต้องการเปลี่ยนรูปภาพ กรุณาใช้ "แก้ไขแบบเต็ม"
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">ยกเลิก</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>บันทึก
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/services/index.blade.php ENDPATH**/ ?>