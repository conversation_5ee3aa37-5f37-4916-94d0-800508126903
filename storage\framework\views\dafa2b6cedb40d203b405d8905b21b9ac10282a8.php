

<?php $__env->startSection('title', 'ดูรายละเอียดข้อความติดต่อ - Admin Panel'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-envelope-open me-2"></i>รายละเอียดข้อความติดต่อ
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">แดชบอร์ด</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.contacts.index')); ?>">จัดการข้อความติดต่อ</a></li>
                        <li class="breadcrumb-item active">รายละเอียด</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                ข้อความจาก: <?php echo e($contact->name); ?>

                                <?php if(!$contact->is_read): ?>
                                    <span class="badge badge-warning ml-2">ยังไม่อ่าน</span>
                                <?php else: ?>
                                    <span class="badge badge-success ml-2">อ่านแล้ว</span>
                                <?php endif; ?>
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-user text-primary"></i> ข้อมูลผู้ติดต่อ</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>ชื่อ:</strong></td>
                                            <td><?php echo e($contact->name); ?></td>
                                        </tr>
                                        <?php if($contact->phone): ?>
                                        <tr>
                                            <td><strong>เบอร์โทร:</strong></td>
                                            <td>
                                                <a href="tel:<?php echo e($contact->phone); ?>" class="text-decoration-none">
                                                    <i class="fas fa-phone text-success"></i> <?php echo e($contact->phone); ?>

                                                </a>
                                            </td>
                                        </tr>
                                        <?php endif; ?>
                                        <?php if($contact->email): ?>
                                        <tr>
                                            <td><strong>อีเมล:</strong></td>
                                            <td>
                                                <a href="mailto:<?php echo e($contact->email); ?>" class="text-decoration-none">
                                                    <i class="fas fa-envelope text-info"></i> <?php echo e($contact->email); ?>

                                                </a>
                                            </td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-clock text-warning"></i> ข้อมูลเวลา</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>ส่งเมื่อ:</strong></td>
                                            <td><?php echo e($contact->created_at->format('d/m/Y H:i:s')); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>เวลาที่ผ่านมา:</strong></td>
                                            <td><?php echo e($contact->created_at->diffForHumans()); ?></td>
                                        </tr>
                                        <?php if($contact->updated_at != $contact->created_at): ?>
                                        <tr>
                                            <td><strong>อัปเดตล่าสุด:</strong></td>
                                            <td><?php echo e($contact->updated_at->format('d/m/Y H:i:s')); ?></td>
                                        </tr>
                                        <?php endif; ?>
                                    </table>
                                </div>
                            </div>

                            <hr>

                            <h6><i class="fas fa-comment text-success"></i> ข้อความ</h6>
                            <div class="bg-light p-3 rounded">
                                <p class="mb-0"><?php echo e($contact->message ?: 'ไม่มีข้อความ'); ?></p>
                            </div>
                        </div>
                        <div class="card-footer">
                            <?php if(!$contact->is_read): ?>
                                <form action="<?php echo e(route('admin.contacts.update', $contact)); ?>" method="POST" style="display:inline;">
                                    <?php echo csrf_field(); ?> <?php echo method_field('PUT'); ?>
                                    <input type="hidden" name="is_read" value="1">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-check me-1"></i>ทำเครื่องหมายว่าอ่านแล้ว
                                    </button>
                                </form>
                            <?php endif; ?>
                            <a href="<?php echo e(route('admin.contacts.index')); ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>ย้อนกลับ
                            </a>
                            <form action="<?php echo e(route('admin.contacts.destroy', $contact)); ?>" method="POST" style="display:inline;" onsubmit="return confirm('ยืนยันการลบข้อความนี้?')">
                                <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash me-1"></i>ลบข้อความ
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">การดำเนินการ</h3>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <?php if($contact->phone): ?>
                                <a href="tel:<?php echo e($contact->phone); ?>" class="list-group-item list-group-item-action">
                                    <i class="fas fa-phone text-success"></i> โทรหา <?php echo e($contact->name); ?>

                                </a>
                                <?php endif; ?>
                                <?php if($contact->email): ?>
                                <a href="mailto:<?php echo e($contact->email); ?>" class="list-group-item list-group-item-action">
                                    <i class="fas fa-envelope text-info"></i> ส่งอีเมลถึง <?php echo e($contact->name); ?>

                                </a>
                                <?php endif; ?>
                                <a href="<?php echo e(route('admin.contacts.index')); ?>" class="list-group-item list-group-item-action">
                                    <i class="fas fa-list text-primary"></i> ดูข้อความทั้งหมด
                                </a>
                            </div>
                        </div>
                    </div>

                    <?php if($contact->phone || $contact->email): ?>
                    <div class="card mt-3">
                        <div class="card-header">
                            <h3 class="card-title">ข้อมูลติดต่อ</h3>
                        </div>
                        <div class="card-body">
                            <div class="text-center">
                                <h5><?php echo e($contact->name); ?></h5>
                                <?php if($contact->phone): ?>
                                    <p class="mb-1">
                                        <i class="fas fa-phone text-success"></i> <?php echo e($contact->phone); ?>

                                    </p>
                                <?php endif; ?>
                                <?php if($contact->email): ?>
                                    <p class="mb-0">
                                        <i class="fas fa-envelope text-info"></i> <?php echo e($contact->email); ?>

                                    </p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/contacts/edit.blade.php ENDPATH**/ ?>