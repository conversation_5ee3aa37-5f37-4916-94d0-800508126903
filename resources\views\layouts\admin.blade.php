<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Admin Panel - SoloShop')</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- AdminLTE CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">
    <!-- Enhanced Admin CSS -->
    <link rel="stylesheet" href="{{ asset('css/admin-enhanced.css') }}">
    
    <style>
        body {
            font-family: 'Sarabun', sans-serif;
        }
        
        .brand-link {
            padding: 0.8125rem 0.5rem;
            font-size: 1.1rem;
            line-height: 1.5;
            white-space: nowrap;
            border-bottom: 1px solid #4f5962;
            display: block;
            color: rgba(255,255,255,.8);
            text-decoration: none;
        }
        
        .brand-text {
            font-weight: 400;
            margin-left: 0.5rem;
        }
        
        .main-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            min-height: 100%;
            z-index: 1037;
            width: 250px;
        }
        
        .content-wrapper {
            margin-left: 250px;
            min-height: 100vh;
        }
        
        .small-box {
            border-radius: 0.25rem;
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
            display: block;
            margin-bottom: 20px;
            position: relative;
        }
        
        .small-box > .inner {
            padding: 10px;
        }
        
        .small-box > .small-box-footer {
            background-color: rgba(0,0,0,.1);
            color: rgba(255,255,255,.8);
            display: block;
            padding: 3px 0;
            position: relative;
            text-align: center;
            text-decoration: none;
            z-index: 10;
        }
        
        .small-box .icon {
            color: rgba(0,0,0,.15);
            z-index: 0;
        }
        
        .small-box .icon > i {
            font-size: 70px;
            position: absolute;
            right: 15px;
            top: 15px;
        }
        
        .bg-info {
            background-color: #17a2b8!important;
        }
        
        .bg-success {
            background-color: #28a745!important;
        }
        
        .bg-warning {
            background-color: #ffc107!important;
        }
        
        .bg-danger {
            background-color: #dc3545!important;
        }
        
        .small-box h3 {
            font-size: 2.2rem;
            font-weight: 700;
            margin: 0 0 10px 0;
            white-space: nowrap;
            padding: 0;
            color: #fff;
        }
        
        .small-box p {
            font-size: 1rem;
            color: #fff;
        }
        
        .card {
            box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
            margin-bottom: 1rem;
        }
        
        .card-header {
            background-color: transparent;
            border-bottom: 1px solid rgba(0,0,0,.125);
            padding: 0.75rem 1.25rem;
            position: relative;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
        }
        
        .btn-block {
            display: block;
            width: 100%;
        }
        
        .content-header {
            padding: 15px 0.5rem;
        }
        
        .breadcrumb {
            background-color: transparent;
            margin-bottom: 0;
            padding: 0;
        }

        /* Enhanced Menu Styling */
        .main-sidebar {
            background: linear-gradient(180deg, #343a40 0%, #495057 100%);
        }

        .brand-link {
            background: rgba(255,255,255,0.1);
            transition: all 0.3s ease;
        }

        .brand-link:hover {
            background: rgba(255,255,255,0.15);
            text-decoration: none;
        }

        .nav-sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 2px 8px;
            padding: 10px 15px;
        }

        .nav-sidebar .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: #fff;
            transform: translateX(3px);
        }

        .nav-sidebar .nav-link.active {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: #fff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.3);
        }

        .nav-header {
            color: rgba(255,255,255,0.9);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin: 1.5rem 0 0.5rem 0;
            padding: 0 15px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            padding-bottom: 8px;
        }

        .nav-header i {
            margin-right: 5px;
            opacity: 0.7;
        }

        /* Submenu styling */
        .nav-treeview {
            padding-left: 0;
        }

        .nav-treeview .nav-link {
            padding-left: 35px;
            font-size: 0.9rem;
            color: rgba(255,255,255,0.7);
        }

        .nav-treeview .nav-link:hover {
            color: #fff;
            background: rgba(255,255,255,0.08);
        }

        .nav-treeview .nav-link.active {
            background: rgba(0,123,255,0.8);
            color: #fff;
        }

        /* Icon colors */
        .nav-icon.text-info { color: #17a2b8 !important; }
        .nav-icon.text-success { color: #28a745 !important; }
        .nav-icon.text-primary { color: #007bff !important; }
        .nav-icon.text-warning { color: #ffc107 !important; }
        .nav-icon.text-danger { color: #dc3545 !important; }
        .nav-icon.text-secondary { color: #6c757d !important; }

        .user-panel {
            border-bottom: 1px solid rgba(255,255,255,0.1);
            padding: 15px;
        }

        .user-panel .info a {
            color: #fff;
            text-decoration: none;
            font-weight: 500;
        }

        .user-panel .info small {
            color: rgba(255,255,255,0.7);
            font-size: 0.8rem;
        }

        /* Animation for menu items */
        .nav-sidebar .nav-item {
            transition: all 0.3s ease;
        }

        .nav-sidebar .nav-link i {
            transition: all 0.3s ease;
        }

        .nav-sidebar .nav-link:hover i {
            transform: scale(1.1);
        }

        /* Menu expand/collapse animation */
        .nav-item.menu-open > .nav-treeview {
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }
            to {
                opacity: 1;
                max-height: 200px;
            }
        }

        /* Badge animation */
        .badge {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body class="hold-transition sidebar-mini">
    <div class="wrapper">
        <!-- Main Sidebar Container -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <a href="/admin" class="brand-link text-center">
                <i class="fas fa-cogs fa-lg"></i>
                <span class="brand-text">SoloShop Admin</span>
            </a>
            
            <div class="sidebar">
                <!-- User Panel -->
                <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                    <div class="image">
                        <i class="fas fa-user-circle fa-2x text-white"></i>
                    </div>
                    <div class="info">
                        <a href="#" class="d-block text-white">{{ Auth::user()->name }}</a>
                        <small class="text-light">Administrator</small>
                    </div>
                </div>

                <!-- Sidebar Menu -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                        <!-- Dashboard -->
                        <li class="nav-item">
                            <a href="{{ route('admin.dashboard') }}" class="nav-link {{ request()->is('admin') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-tachometer-alt text-info"></i>
                                <p>แดชบอร์ด</p>
                            </a>
                        </li>

                        <!-- จัดการเนื้อหา -->
                        <li class="nav-header text-uppercase font-weight-bold">
                            <i class="fas fa-edit mr-1"></i>จัดการเนื้อหา
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('admin.homepage.index') }}" class="nav-link {{ request()->is('admin/homepage*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-home text-success"></i>
                                <p>หน้าแรก</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('admin.services.index') }}" class="nav-link {{ request()->is('admin/services*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-tools text-primary"></i>
                                <p>บริการ</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('admin.packages.index') }}" class="nav-link {{ request()->is('admin/packages*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-box text-warning"></i>
                                <p>แพ็กเกจ</p>
                            </a>
                        </li>

                        <!-- กิจกรรม (มี submenu) -->
                        <li class="nav-item {{ request()->is('admin/activities*') || request()->is('admin/activity-categories*') ? 'menu-open' : '' }}">
                            <a href="#" class="nav-link {{ request()->is('admin/activities*') || request()->is('admin/activity-categories*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-images text-info"></i>
                                <p>
                                    กิจกรรม
                                    <i class="fas fa-angle-left right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a href="{{ route('admin.activities.index') }}" class="nav-link {{ request()->is('admin/activities*') ? 'active' : '' }}">
                                        <i class="far fa-circle nav-icon text-info"></i>
                                        <p>รายการกิจกรรม</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ route('admin.activity-categories.index') }}" class="nav-link {{ request()->is('admin/activity-categories*') ? 'active' : '' }}">
                                        <i class="far fa-circle nav-icon text-secondary"></i>
                                        <p>หมวดหมู่กิจกรรม</p>
                                    </a>
                                </li>
                            </ul>
                        </li>

                        <!-- จัดการข้อมูล -->
                        <li class="nav-header text-uppercase font-weight-bold">
                            <i class="fas fa-database mr-1"></i>จัดการข้อมูล
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('admin.contacts.index') }}" class="nav-link {{ request()->is('admin/contacts*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-envelope text-danger"></i>
                                <p>
                                    ข้อความติดต่อ
                                    @php
                                        $unreadCount = \App\Models\Contact::where('is_read', false)->count();
                                    @endphp
                                    @if($unreadCount > 0)
                                        <span class="badge badge-danger right">{{ $unreadCount }}</span>
                                    @endif
                                </p>
                            </a>
                        </li>

                        <!-- การตั้งค่า -->
                        <li class="nav-header text-uppercase font-weight-bold">
                            <i class="fas fa-cogs mr-1"></i>การตั้งค่า
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('admin.settings.index') }}" class="nav-link {{ request()->is('admin/settings*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-cog text-secondary"></i>
                                <p>การตั้งค่าเว็บไซต์</p>
                            </a>
                        </li>

                        <!-- บัญชีผู้ใช้ -->
                        <li class="nav-header text-uppercase font-weight-bold">
                            <i class="fas fa-user mr-1"></i>บัญชีผู้ใช้
                        </li>

                        <li class="nav-item">
                            <a href="{{ url('/') }}" class="nav-link" target="_blank">
                                <i class="nav-icon fas fa-external-link-alt text-success"></i>
                                <p>ดูเว็บไซต์</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('logout') }}" class="nav-link"
                               onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                <i class="nav-icon fas fa-sign-out-alt text-danger"></i>
                                <p>ออกจากระบบ</p>
                            </a>
                            <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                @csrf
                            </form>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- Content Wrapper -->
        <div class="content-wrapper">
            @yield('content')
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE JS -->
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>
    <!-- Admin Common JS -->
    <script src="{{ asset('js/admin-common.js') }}"></script>

    @stack('scripts')
</body>
</html>
